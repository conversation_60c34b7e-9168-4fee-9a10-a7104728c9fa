<?php

namespace Modules\Yizhan\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Http\Middleware\RefreshToken;
use Symfony\Component\HttpFoundation\Response;

class ModuleAuthMiddleware
{
    protected $refreshTokenMiddleware;

    public function __construct(RefreshToken $refreshTokenMiddleware)
    {
        $this->refreshTokenMiddleware = $refreshTokenMiddleware;
    }

    /**
     * Handle an incoming request.
     * 使用父模块的认证逻辑
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 直接使用父模块的 RefreshToken 中间件
        return $this->refreshTokenMiddleware->handle($request, $next);
    }
}
