<?php

namespace Modules\Yizhan\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class YizhanController extends BaseController
{
    /**
     * 获取用户信息
     */
    public function getUserInfo(Request $request): JsonResponse
    {
        $user = $this->getCurrentUser($request);

        if (!$user) {
            return $this->error('用户未登录', 401);
        }

        return $this->success([
            'user' => $user->only(['id', 'username', 'real_name', 'email']),
            'organization' => $user->organization,
            'roles' => $user->roles->pluck('name'),
            'permissions' => $user->getAllPermissions()->pluck('name')
        ]);
    }

    /**
     * 获取模块状态（需要特定权限）
     */
    public function getModuleStatus(Request $request): JsonResponse
    {
        // 检查权限
        if (!$this->checkPermission($request, 'yizhan.admin')) {
            return $this->error('权限不足', 403);
        }

        return $this->success([
            'module' => 'Yizhan',
            'status' => 'active',
            'version' => '1.0.0',
            'user' => $this->getCurrentUser($request)->only(['id', 'username', 'real_name'])
        ]);
    }

    /**
     * 获取机构数据（检查机构权限）
     */
    public function getOrganizationData(Request $request): JsonResponse
    {
        $organizationId = $this->getOrganizationId($request);

        if (!$organizationId) {
            return $this->error('用户未绑定机构', 400);
        }

        // 这里可以添加更多的机构数据逻辑
        return $this->success([
            'organization_id' => $organizationId,
            'data' => [
                'name' => '示例机构',
                'type' => 'school',
                'status' => 'active'
            ]
        ]);
    }

    /**
     * 管理员专用接口
     */
    public function adminDashboard(Request $request): JsonResponse
    {
        // 检查是否有管理员角色
        if (!$this->hasRole($request, 'admin')) {
            return $this->error('需要管理员权限', 403);
        }

        return $this->success([
            'dashboard' => 'admin',
            'user' => $this->getCurrentUser($request)->only(['id', 'username', 'real_name']),
            'stats' => [
                'total_users' => 100,
                'active_sessions' => 25,
                'module_version' => '1.0.0'
            ]
        ]);
    }
}
