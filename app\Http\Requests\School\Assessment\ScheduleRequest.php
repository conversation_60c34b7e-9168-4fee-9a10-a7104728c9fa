<?php

namespace App\Http\Requests\School\Assessment;

use App\Http\Requests\BaseRequest;

class ScheduleRequest extends BaseRequest
{

    public function rules(): array
    {
        return [
            'schedule_name' => 'required|string',
            'open_time' => 'required|date',
            'close_time' => 'required|date|after:open_time',
            'is_report_visible' => 'required|integer',
            'type' => 'required|integer',
            'assessment_ids' => 'required|array',
            'student_info_arr' => 'required|array',
            'student_info_arr.*.student_class_id' => 'required|integer',
            'student_info_arr.*.student_id' => 'required|integer',
            'student_info_arr.*.user_id' => 'required|integer',
        ];
    }

    public function messages(): array
    {
        return [
            'schedule_name.required' => '计划名称不能为空',
            'open_time.required' => '开始时间不能为空',
            'close_time.required' => '结束时间不能为空',
            'close_time.after' => '结束时间必须晚于开始时间',
            'is_report_visible.required' => '是否展示字段不能为空',
            'is_report_visible.integer' => '是否展示字段值为0或1',
            'type.required' => 'type字段不能为空',
            'type.integer' => 'type字段值为1非心理测评或2心理测评',
            'assessment_ids.required' => '评估信息不能为空',
            'student_info_arr.required' => '学生信息不能为空',
            'student_info_arr.*.student_class_id.integer' => '学生班级id必须为整数',
            'student_info_arr.*.student_id.integer' => '学生id必须为整数',
            'student_info_arr.*.user_id.integer' => '用户id必须为整数',
        ];
    }
}
