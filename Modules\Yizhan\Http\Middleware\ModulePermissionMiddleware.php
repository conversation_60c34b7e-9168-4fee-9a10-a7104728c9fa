<?php

namespace Modules\Yizhan\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Traits\ApiResponse;

class ModulePermissionMiddleware
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     * 检查用户是否有访问模块的权限
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return $this->error('未登录', Response::HTTP_UNAUTHORIZED);
        }

        // 如果没有指定权限，则只检查登录状态
        if (empty($permissions)) {
            return $next($request);
        }

        // 检查用户是否有指定权限
        foreach ($permissions as $permission) {
            if (!$user->can($permission)) {
                return $this->error('权限不足', Response::HTTP_FORBIDDEN);
            }
        }

        return $next($request);
    }
}
