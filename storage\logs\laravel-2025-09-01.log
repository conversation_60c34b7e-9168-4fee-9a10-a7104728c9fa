[2025-09-01 16:43:35] local.ERROR: Trait "Illuminate\Console\Prohibitable" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Illuminate\\Console\\Prohibitable\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Commands\\BaseCommand.php:18)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:11:34] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 {main}
"} 
[2025-09-01 17:11:35] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:11:35] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:12:04] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 {main}
"} 
[2025-09-01 17:12:05] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:12:05] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:12:40] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 {main}
"} 
[2025-09-01 17:12:41] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:12:41] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:15:25] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2025-09-01 17:15:57] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2025-09-01 17:17:04] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 {main}
"} 
[2025-09-01 17:21:36] local.ERROR: The "--compact" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--compact\" option does not exist. at E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Input\\ArgvInput.php:223)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Input\\ArgvInput.php(152): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('compact', NULL)
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Input\\ArgvInput.php(85): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--compact')
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Input\\ArgvInput.php(74): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--compact', true)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Input\\Input.php(56): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Command\\Command.php(285): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 E:\\YuanBoCode\\yishengya2024\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-09-01 17:27:00] local.ERROR: require(/yizhan): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(/yizhan): Failed to open stream: No such file or directory at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(/yizhan...', 'E:\\\\YuanBoCode\\\\y...', 35)
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(/yizhan...', 'E:\\\\YuanBoCode\\\\y...', 35)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('E:\\\\YuanBoCode\\\\y...')
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/yizhan')
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/yizhan')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/yizhan')
#6 E:\\YuanBoCode\\yishengya2024\\app\\Providers\\RouteServiceProvider.php(32): Illuminate\\Routing\\RouteRegistrar->group('/yizhan')
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Routing\\Router->App\\Providers\\{closure}('/yizhan', Object(Closure))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(1491): Illuminate\\Routing\\Router->macroCall('authApi', Array)
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->__call('authApi', Array)
#10 E:\\YuanBoCode\\yishengya2024\\Modules\\Yizhan\\Routes\\api.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('authApi', Array)
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('E:\\\\YuanBoCode\\\\y...')
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('E:\\\\YuanBoCode\\\\y...')
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('E:\\\\YuanBoCode\\\\y...')
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'E:\\\\YuanBoCode\\\\y...')
#15 E:\\YuanBoCode\\yishengya2024\\Modules\\Yizhan\\Providers\\RouteServiceProvider.php(67): Illuminate\\Routing\\RouteRegistrar->group('E:\\\\YuanBoCode\\\\y...')
#16 E:\\YuanBoCode\\yishengya2024\\Modules\\Yizhan\\Providers\\RouteServiceProvider.php(36): Modules\\Yizhan\\Providers\\RouteServiceProvider->mapApiRoutes()
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Modules\\Yizhan\\Providers\\RouteServiceProvider->map()
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Modules\\Yizhan\\Providers\\RouteServiceProvider))
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Modules\\Yizhan\\Providers\\RouteServiceProvider), 41)
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-09-01 17:27:01] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(ErrorException))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(ErrorException))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:27:01] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:27:33] local.ERROR: require(/yizhan): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): require(/yizhan): Failed to open stream: No such file or directory at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php:35)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(/yizhan...', 'E:\\\\YuanBoCode\\\\y...', 35)
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'require(/yizhan...', 'E:\\\\YuanBoCode\\\\y...', 35)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('E:\\\\YuanBoCode\\\\y...')
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('/yizhan')
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('/yizhan')
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, '/yizhan')
#6 E:\\YuanBoCode\\yishengya2024\\app\\Providers\\RouteServiceProvider.php(32): Illuminate\\Routing\\RouteRegistrar->group('/yizhan')
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Macroable\\Traits\\Macroable.php(123): Illuminate\\Routing\\Router->App\\Providers\\{closure}('/yizhan', Object(Closure))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(1491): Illuminate\\Routing\\Router->macroCall('authApi', Array)
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Routing\\Router->__call('authApi', Array)
#10 E:\\YuanBoCode\\yishengya2024\\Modules\\Yizhan\\Routes\\api.php(16): Illuminate\\Support\\Facades\\Facade::__callStatic('authApi', Array)
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(35): require('E:\\\\YuanBoCode\\\\y...')
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Routing\\RouteFileRegistrar->register('E:\\\\YuanBoCode\\\\y...')
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(465): Illuminate\\Routing\\Router->loadRoutes('E:\\\\YuanBoCode\\\\y...')
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(194): Illuminate\\Routing\\Router->group(Array, 'E:\\\\YuanBoCode\\\\y...')
#15 E:\\YuanBoCode\\yishengya2024\\Modules\\Yizhan\\Providers\\RouteServiceProvider.php(67): Illuminate\\Routing\\RouteRegistrar->group('E:\\\\YuanBoCode\\\\y...')
#16 E:\\YuanBoCode\\yishengya2024\\Modules\\Yizhan\\Providers\\RouteServiceProvider.php(36): Modules\\Yizhan\\Providers\\RouteServiceProvider->mapApiRoutes()
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Modules\\Yizhan\\Providers\\RouteServiceProvider->map()
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(122): Illuminate\\Container\\Container->call(Array)
#23 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(45): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#24 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#25 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#28 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#29 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(119): Illuminate\\Container\\Container->call(Object(Closure))
#30 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1062): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#31 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Modules\\Yizhan\\Providers\\RouteServiceProvider))
#32 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Modules\\Yizhan\\Providers\\RouteServiceProvider), 41)
#33 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#34 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#35 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#36 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#37 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#38 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-09-01 17:27:34] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(ErrorException))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(ErrorException))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:27:34] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
