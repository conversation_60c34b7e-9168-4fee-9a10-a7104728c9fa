[2025-09-01 16:43:35] local.ERROR: Trait "Illuminate\Console\Prohibitable" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Illuminate\\Console\\Prohibitable\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Commands\\BaseCommand.php:18)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:11:34] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 {main}
"} 
[2025-09-01 17:11:35] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:11:35] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:12:04] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 {main}
"} 
[2025-09-01 17:12:05] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:12:05] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
[2025-09-01 17:12:40] local.ERROR: Class "Modules\Yizhan\Providers\YizhanServiceProvider" not found {"exception":"[object] (Error(code: 0): Class \"Modules\\Yizhan\\Providers\\YizhanServiceProvider\" not found at E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php:206)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(142): Illuminate\\Foundation\\ProviderRepository->createProvider('Modules\\\\Yizhan\\\\...')
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(61): Illuminate\\Foundation\\ProviderRepository->compileManifest(Array)
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Laravel\\Module.php(33): Illuminate\\Foundation\\ProviderRepository->load(Array)
#3 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Module.php(244): Nwidart\\Modules\\Laravel\\Module->registerProviders()
#4 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\FileRepository.php(321): Nwidart\\Modules\\Module->register()
#5 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\Providers\\BootstrapServiceProvider.php(23): Nwidart\\Modules\\FileRepository->register()
#6 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Nwidart\\Modules\\Providers\\BootstrapServiceProvider->register()
#7 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\ModulesServiceProvider.php(31): Illuminate\\Foundation\\Application->register(Object(Nwidart\\Modules\\Providers\\BootstrapServiceProvider))
#8 E:\\YuanBoCode\\yishengya2024\\vendor\\nwidart\\laravel-modules\\src\\LaravelModulesServiceProvider.php(17): Nwidart\\Modules\\ModulesServiceProvider->registerModules()
#9 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Nwidart\\Modules\\LaravelModulesServiceProvider->boot()
#10 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1059): Illuminate\\Container\\Container->call(Array)
#15 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1040): Illuminate\\Foundation\\Application->bootProvider(Object(Nwidart\\Modules\\LaravelModulesServiceProvider))
#16 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Nwidart\\Modules\\LaravelModulesServiceProvider), 25)
#17 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1039): array_walk(Array, Object(Closure))
#18 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#19 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#20 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#21 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#22 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#23 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#24 {main}
"} 
[2025-09-01 17:12:41] local.ERROR: Call to a member function getName() on null {"exception":"[object] (Error(code: 0): Call to a member function getName() on null at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(509): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(148): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#3 {main}
"} 
[2025-09-01 17:12:41] local.ERROR: Uncaught Error: Call to a member function getName() on null in E:\YuanBoCode\yishengya2024\app\Exceptions\Handler.php:55
Stack trace:
#0 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(218): App\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(Error))
#1 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(195): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\YuanBoCode\yishengya2024\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(255): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Error))
#4 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught Error: Call to a member function getName() on null in E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55
Stack trace:
#0 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(218): App\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Error))
#1 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(195): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Error))
#2 E:\\YuanBoCode\\yishengya2024\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Error))
#3 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Error))
#4 {main}
  thrown at E:\\YuanBoCode\\yishengya2024\\app\\Exceptions\\Handler.php:55)
[stacktrace]
#0 {main}
"} 
