<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
// 使用与父模块完全一致的配置
Route::authApi('/yizhan', function(Request $request) {
     return $request->user();
});
// Route::middleware('auth:api')->get('/yizhan', function (Request $request) {
//     return $request->user();
// });