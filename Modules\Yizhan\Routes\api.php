<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// 使用父模块的 authApi 宏 - 完全继承父模块的认证体系
Route::authApi(function () {
    Route::prefix('yizhan')->group(function () {
        // 基础认证路由 - 只需要登录
        Route::get('user', '<PERSON><PERSON><PERSON><PERSON><PERSON>roller@getUserInfo')->name('yizhan.user');
        Route::get('organization', 'YizhanController@getOrganizationData')->name('yizhan.organization');
    });
});

// 管理员路由 - 也使用父模块的 authApi 宏
Route::authApi(function () {
    Route::prefix('yizhan/admin')->group(function () {
        Route::get('status', 'YizhanController@getModuleStatus')->name('yizhan.admin.status');
        Route::get('dashboard', 'YizhanController@adminDashboard')->name('yizhan.admin.dashboard');
    });
});

// 公共接口 - 同样使用父模块的 authApi 宏
Route::authApi(function () {
    Route::prefix('yizhan/public')->group(function () {
        Route::get('status', function () {
            return response()->json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'module' => 'Yizhan',
                    'status' => 'active',
                    'version' => '1.0.0'
                ]
            ]);
        })->name('yizhan.public.status');
    });
});