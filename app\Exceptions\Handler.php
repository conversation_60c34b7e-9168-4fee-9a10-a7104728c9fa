<?php

namespace App\Exceptions;

use App\Traits\ApiResponse;
use App\Traits\DingDingMessage;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Handler extends ExceptionHandler
{
    use DingDingMessage;
    use ApiResponse;

    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'password',
        'current_password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * 捕获异常，存储数据到数据库，方便后期排查问题
     * @param $request
     * @param Throwable $e
     */
    public function render($request, Throwable $e)
    {
        if (env('APP_DEBUG')) {
            //  开发环境，显示详细错误信息
            return parent::render($request, $e);
        } else {
            //  生产环境，未知错误，显示500
            $ip = $request->header('X-Forwarded-For') ?: $request->getClientIp();
            // 获取服务器ip
            $server_ip = gethostbyname(gethostname());

            // todo：记录错误日志 OR 钉钉通知
            try {
                $routeName  = $request->route()->getName();
                $str        = '- 操作人: ' . ($request->user() ? $request->user()->username : '未登录') . "\n"
                            . '- 操作时间: ' . date('Y-m-d H:i:s') . "\n"
                            . '- 访问了: ' . $routeName . "\n"
                            . '- ip地址: ' . $request->ip() . "\n"
                            . '- 服务器ip: '. $server_ip. "\n"
                            . '- 请求方式: ' . $request->getMethod() . "\n"
                            . '- 错误接口: ' . urldecode($request->fullUrl()) . "\n"
                            . '- 错误信息: ' . substr($e->getMessage(), 0, 500) . "\n";
                $this->send_dingding_message($str);
            } catch (\Exception $e) {

            }

            
            return $this->error($e->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
