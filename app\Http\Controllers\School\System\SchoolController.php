<?php

namespace App\Http\Controllers\School\System;

use App\Http\Controllers\Controller;
use App\Models\School\System\School;
use App\Services\School\System\SchoolService;
use App\Traits\CrudOperations;
use Illuminate\Http\Request;
use App\Http\Requests\School\System\SchoolRequest;

class SchoolController extends Controller
{

    use CrudOperations;

    protected string $model = School::class;

    protected $schoolService;

    // 构造函数注入 SchoolService
    public function __construct(SchoolService $schoolService)
    {
        $this->schoolService = $schoolService;
    }

    public function detail(Request $request)
    {
        // 获取当前登录用户所属学校id
        $school_id = $request->user()->organization->model_id;
        $record = $this->model::whereId($school_id)->with(['config'])->get();
        return $this->success($record);
    }

    public function update(Request $request)
    {
        // 获取当前登录用户所属学校id
        $school_id = $request->user()->organization->model_id;

        $this->schoolService->updateSchool($request, $school_id);
        return $this->message("更新成功");
    }

    public function setConfig(Request $request)
    {
        // TODO
        return $this->success("");
    }

    public function show($id)
    {
        $record = $this->model::with(['organization'])->find($id);
        if (!$record) {
            return $this->notFound('查询对象不存在');
        }
        return $this->success($record);
    }


    /**
     * 获取班级数据，用于班级列表穿梭框使用
     * 返回层级结构：校区->年级->班级（不包含学生）
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function classTransferData(SchoolRequest $request)
    {
        $data = $this->schoolService->classTransferData($request);
        return $this->success($data);
    }

    /**
     * 获取学校下拉数据
     * 返回学校id和name，用于下拉选择
     * 支持按省市区过滤
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSchoolOptions(Request $request)
    {
        $query = $this->model::query();

        // 支持按省市区过滤
        if ($request->has('province')) {
            $query->where('province', $request->province);
        }
        if ($request->has('city')) {
            $query->where('city', $request->city);
        }
        if ($request->has('district')) {
            $query->where('district', $request->district);
        }

        $schools = $query->select('id', 'name')
            ->orderBy('name', 'asc')
            ->get();

        return $this->success($schools);
    }

    // 学年列表
    public function schoolYearList()
    {
        $currentSchoolYear = getCurrentSchoolYear();
        // 获取当前学年前三年，并封装到一个数组中
        $data = [
            $currentSchoolYear,
            $currentSchoolYear - 1,
            $currentSchoolYear - 2,
            $currentSchoolYear - 3,
            $currentSchoolYear - 4,
            $currentSchoolYear - 5,
            $currentSchoolYear - 6
        ];
        return $this->success($data);
    }
}
