<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to your application's "home" route.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        // 注册认证API路由宏（使用项目实际的JWT认证体系）
        Route::macro('authApi', function($callback) {
            return Route::middleware(['api', 'auth.refresh', 'access_log', 'throttle:500'])
                ->prefix('api')
                ->name('api.')
                ->group($callback);
        });

        RateLimiter::for('api', function (Request $request) {
            // 限制每个用户每分钟最多请求 60 次，使用用户 ID 作为唯一标识符
            // 限制每个 IP 地址每分钟最多请求 60 次，使用 IP 地址作为唯一标识符
            return Limit::perMinute(60)->by($request->user() ? $request->user()->id : $request->ip());
        });

        $this->routes(function () {
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            // 加载 evaluation 模块路由（不使用 api 前缀）
            Route::middleware('api')
                ->group(base_path('routes/evaluation.php'));
        });
    }
}
