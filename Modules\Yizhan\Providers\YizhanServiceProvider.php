<?php

namespace Modules\Yizhan\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;

class YizhanServiceProvider extends ServiceProvider
{
    /**
     * @var string $moduleName
     */
    protected $moduleName = 'Yizhan';

    /**
     * @var string $moduleNameLower
     */
    protected $moduleNameLower = 'yizhan';

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'Database/Migrations'));
        $this->registerRouteMacros();
    }

    /**
     * Register route macros for module
     *
     * @return void
     */
    protected function registerRouteMacros()
    {
        // 注册模块专用的认证路由宏
        \Route::macro('moduleAuthApi', function($prefix, $callback) {
            return \Route::prefix('api/' . ltrim($prefix, '/'))
                ->middleware(['api', 'module.auth', 'access_log', 'throttle:500'])
                ->group($callback);
        });

        // 注册带权限检查的路由宏
        \Route::macro('modulePermissionApi', function($prefix, $permissions, $callback) {
            $middleware = ['api', 'module.auth', 'access_log', 'throttle:500'];
            if (!empty($permissions)) {
                $middleware[] = 'module.permission:' . implode(',', (array)$permissions);
            }

            return \Route::prefix('api/' . ltrim($prefix, '/'))
                ->middleware($middleware)
                ->group($callback);
        });
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
        $this->registerMiddleware();
    }

    /**
     * Register module middleware
     *
     * @return void
     */
    protected function registerMiddleware()
    {
        $router = $this->app['router'];

        // 注册模块专用中间件
        $router->aliasMiddleware('module.auth', \Modules\Yizhan\Http\Middleware\ModuleAuthMiddleware::class);
        $router->aliasMiddleware('module.permission', \Modules\Yizhan\Http\Middleware\ModulePermissionMiddleware::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path($this->moduleName, 'Config/config.php') => config_path($this->moduleNameLower . '.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/config.php'), $this->moduleNameLower
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/' . $this->moduleNameLower);

        $sourcePath = module_path($this->moduleName, 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ], ['views', $this->moduleNameLower . '-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/' . $this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
            $this->loadJsonTranslationsFrom($langPath);
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'Resources/lang'), $this->moduleNameLower);
            $this->loadJsonTranslationsFrom(module_path($this->moduleName, 'Resources/lang'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (\Config::get('view.paths') as $path) {
            if (is_dir($path . '/modules/' . $this->moduleNameLower)) {
                $paths[] = $path . '/modules/' . $this->moduleNameLower;
            }
        }
        return $paths;
    }
}
