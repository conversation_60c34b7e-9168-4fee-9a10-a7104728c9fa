<?php

namespace Modules\Yizhan\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;

class BaseController extends Controller
{
    use ApiResponse;

    /**
     * 获取当前登录用户
     */
    protected function getCurrentUser(Request $request)
    {
        return $request->user();
    }

    /**
     * 检查用户权限
     */
    protected function checkPermission(Request $request, $permission)
    {
        $user = $this->getCurrentUser($request);
        
        if (!$user) {
            return false;
        }

        return $user->can($permission);
    }

    /**
     * 获取用户所属机构ID
     */
    protected function getOrganizationId(Request $request)
    {
        $user = $this->getCurrentUser($request);
        return $user ? $user->organization_id : null;
    }

    /**
     * 检查用户是否属于指定机构
     */
    protected function checkOrganization(Request $request, $organizationId)
    {
        return $this->getOrganizationId($request) === $organizationId;
    }

    /**
     * 获取用户角色
     */
    protected function getUserRoles(Request $request)
    {
        $user = $this->getCurrentUser($request);
        return $user ? $user->roles : collect();
    }

    /**
     * 检查用户是否有指定角色
     */
    protected function hasRole(Request $request, $roleName)
    {
        $user = $this->getCurrentUser($request);
        return $user ? $user->hasRole($roleName) : false;
    }
}
