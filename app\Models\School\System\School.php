<?php

namespace App\Models\School\System;

use App\Models\Admin\Organization;
use App\Models\BaseModel;
use App\Traits\ModelChangeLogTrait;
use App\Models\SiteConfig;
use Illuminate\Database\Eloquent\SoftDeletes;
use Log;

/**
 *
 *
 * @property int $id
 * @property string $name 学校名称
 * @property string|null $address 学校地址
 * @property int $status 是否启用0否1是
 * @property string $date_start 启用日期
 * @property string|null $date_due 到期日期
 * @property string|null $province 省份
 * @property string|null $city 城市
 * @property string|null $district 区县
 * @property int|null $agent_id 代理商id
 * @property int $is_link_gk 是否能跳转第一高考网
 * @property int $site_config_id 站点配置ID
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $creator 添加人
 * @property string $updater 更新人
 * @method static \Illuminate\Database\Eloquent\Builder|School newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|School newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|School pagination()
 * @method static \Illuminate\Database\Eloquent\Builder|School query()
 * @method static \Illuminate\Database\Eloquent\Builder|School whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereAgentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereCreator($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereDateDue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereDateStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereDistrict($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereIsEnable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereIsLinkGk($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereSiteConfigId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|School whereUpdater($value)
 * @mixin \Eloquent
 */
class School extends BaseModel
{
    use SoftDeletes, ModelChangeLogTrait;
    protected $hidden = [
//        'created_at',
//        'updated_at',
//        'creator',
//        'updater',
        'deleted_at'
    ];

    protected $guarded = [ ];

    public function organization()
    {
        return $this->morphOne(Organization::class, 'model');
    }

    // 归属个性化配置
    public function config()
    {
        return $this->belongsTo(SiteConfig::class, 'site_config_id', 'id');
    }

    // 拥有多个校区
    public function schoolCampuses()
    {
        return $this->hasMany(SchoolCampus::class, 'school_id', 'id');
    }

    // 有多个班级
    public function claass()
    {
        return $this->hasMany(Claass::class, 'school_id', 'id');
    }

    /**
     * 根据省、市、区进行过滤的查询范围方法
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string|null $province
     * @param string|null $city
     * @param string|null $district
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFilterByLocation($query, $province = null, $city = null, $district = null)
    {
        if ($province) {
            $query->where('province', $province);
        }
        if ($city) {
            $query->where('city', $city);
        }
        if ($district) {
            $query->where('district', $district);
        }

        return $query;
    }
}
